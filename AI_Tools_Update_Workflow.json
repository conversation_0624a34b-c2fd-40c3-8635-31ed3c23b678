{
  "name": "AI Tools Reference Update",
  "nodes": [
    {
      "parameters": {
        "triggerTimes": {
          "mode": "everyWeek",
          "weekday": "sunday",
          "hour": 0,
          "minute": 0
        }
      },
      "id": "1",
      "name": "Schedule Trigger",
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1,
      "position": [240, 300],
      "notes": "Triggers the workflow every Sunday at 00:00 UTC to start data collection.\n\n**To-Do**:\n- Confirm UTC timezone.\n- Adjust schedule if needed."
    },
    {
      "parameters": {
        "url": "https://lms-leaderboard.com/rankings",
        "options": {
          "retryOnFail": true,
          "retryLimit": 3,
          "retryDelay": 1000
        }
      },
      "id": "2",
      "name": "Scrape LMS Leaderboard",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 2,
      "position": [460, 100],
      "notes": "Scrapes ELO ratings from LMS Leaderboard webpage. Outputs HTML.\n\n**To-Do**:\n- Replace with API URL if available.\n- Verify webpage structure."
    },
    {
      "parameters": {
        "source": "fromPreviousNode",
        "extractionValues": {
          "values": [
            {
              "key": "model_name",
              "cssSelector": ".model-name",
              "returnValue": "text"
            },
            {
              "key": "elo_score",
              "cssSelector": ".elo-score",
              "returnValue": "text"
            }
          ]
        }
      },
      "id": "3",
      "name": "Parse LMS Data",
      "type": "n8n-nodes-base.htmlExtract",
      "typeVersion": 1,
      "position": [680, 100],
      "notes": "Extracts model names and ELO scores from LMS Leaderboard HTML.\n\n**To-Do**:\n- Update CSS selectors based on webpage structure.\n- Test extraction accuracy."
    },
    {
      "parameters": {
        "url": [
          "https://ai.googleblog.com/feed",
          "https://openai.com/blog/rss"
        ],
        "options": {
          "includeContent": true,
          "limit": 50,
          "filter": {
            "conditions": [
              {
                "field": "title",
                "operation": "contains",
                "value": "generative AI"
              },
              {
                "field": "title",
                "operation": "contains",
                "value": "machine learning"
              }
            ]
          }
        }
      },
      "id": "4",
      "name": "Fetch RSS Feeds",
      "type": "n8n-nodes-base.rssFeedRead",
      "typeVersion": 1,
      "position": [460, 300],
      "notes": "Collects AI news from RSS feeds. Outputs articles with title, link, content.\n\n**To-Do**:\n- Add more feed URLs (e.g., TechCrunch AI).\n- Refine keyword filters."
    },
    {
      "parameters": {
        "url": "https://api.huggingface.co/models",
        "authentication": "genericCredentialType",
        "genericAuthType": "httpHeaderAuth",
        "queryParameters": {
          "parameters": [
            {
              "name": "sort",
              "value": "trending"
            },
            {
              "name": "limit",
              "value": 50
            }
          ]
        },
        "options": {
          "retryOnFail": true,
          "retryLimit": 3,
          "retryDelay": 1000
        }
      },
      "id": "5",
      "name": "Fetch Hugging Face Models",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 2,
      "position": [460, 500],
      "credentials": {
        "httpHeaderAuth": {
          "id": "huggingface_api_key",
          "name": "Hugging Face API"
        }
      },
      "notes": "Fetches trending models from Hugging Face API.\n\n**To-Do**:\n- Verify API token.\n- Adjust query parameters for model types."
    },
    {
      "parameters": {
        "mode": "append"
      },
      "id": "6",
      "name": "Merge Data",
      "type": "n8n-nodes-base.merge",
      "typeVersion": 1,
      "position": [900, 300],
      "notes": "Combines data from LMS Leaderboard, RSS feeds, and Hugging Face.\n\n**To-Do**:\n- Ensure JSON structures are compatible."
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "{{ $json.tags || $json.title || '' }}",
              "operation": "contains",
              "value2": "cloud"
            }
          ]
        }
      },
      "id": "7",
      "name": "Filter Cloud APIs",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [1120, 100],
      "notes": "Filters data for Cloud APIs.\n\n**To-Do**:\n- Validate 'cloud' keyword accuracy."
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "{{ $json.tags || $json.title || '' }}",
              "operation": "contains",
              "value2": "local"
            }
          ]
        }
      },
      "id": "8",
      "name": "Filter Local Installations",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [1120, 300],
      "notes": "Filters data for Local Installations.\n\n**To-Do**:\n- Adjust 'local' keyword."
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "{{ $json.tags || $json.title || '' }}",
              "operation": "contains",
              "value2": "saas"
            }
          ]
        }
      },
      "id": "9",
      "name": "Filter SaaS Tools",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [1120, 500],
      "notes": "Filters data for SaaS Tools and Coding Assistants.\n\n**To-Do**:\n- Refine 'saas' keyword."
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "{{ $json.tags || $json.title || '' }}",
              "operation": "contains",
              "value2": "model"
            }
          ]
        }
      },
      "id": "10",
      "name": "Filter AI Models",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [1120, 700],
      "notes": "Filters data for AI Models Comparison.\n\n**To-Do**:\n- Ensure 'model' keyword accuracy."
    },
    {
      "parameters": {
        "values": {
          "string": [
            {
              "name": "category",
              "value": "Cloud APIs"
            },
            {
              "name": "model_name",
              "value": "{{ $json.model_name || $json.title || '' }}"
            },
            {
              "name": "elo_score",
              "value": "{{ $json.elo_score || 'N/A' }}"
            },
            {
              "name": "pricing",
              "value": "{{ $json.pricing || 'N/A' }}"
            }
          ]
        },
        "options": {
          "keepOnlySet": true
        }
      },
      "id": "11",
      "name": "Set Cloud API Data",
      "type": "n8n-nodes-base.set",
      "typeVersion": 1,
      "position": [1340, 100],
      "notes": "Formats data for Cloud APIs with pricing.\n\n**To-Do**:\n- Add fields like api_limits if available."
    },
    {
      "parameters": {
        "values": {
          "string": [
            {
              "name": "category",
              "value": "Local Installations"
            },
            {
              "name": "model_name",
              "value": "{{ $json.model_name || $json.title || '' }}"
            },
            {
              "name": "elo_score",
              "value": "{{ $json.elo_score || 'N/A' }}"
            },
            {
              "name": "hardware_requirements",
              "value": "{{ $json.hardware_requirements || 'N/A' }}"
            }
          ]
        },
        "options": {
          "keepOnlySet": true
        }
      },
      "id": "12",
      "name": "Set Local Installation Data",
      "type": "n8n-nodes-base.set",
      "typeVersion": 1,
      "position": [1340, 300],
      "notes": "Formats data for Local Installations with hardware requirements.\n\n**To-Do**:\n- Include GPU/CPU specs if available."
    },
    {
      "parameters": {
        "values": {
          "string": [
            {
              "name": "category",
              "value": "SaaS Tools"
            },
            {
              "name": "model_name",
              "value": "{{ $json.model_name || $json.title || '' }}"
            },
            {
              "name": "elo_score",
              "value": "{{ $json.elo_score || 'N/A' }}"
            },
            {
              "name": "pricing",
              "value": "{{ $json.pricing || 'N/A' }}"
            }
          ]
        },
        "options": {
          "keepOnlySet": true
        }
      },
      "id": "13",
      "name": "Set SaaS Tools Data",
      "type": "n8n-nodes-base.set",
      "typeVersion": 1,
      "position": [1340, 500],
      "notes": "Formats data for SaaS Tools with pricing.\n\n**To-Do**:\n- Add subscription model details."
    },
    {
      "parameters": {
        "values": {
          "string": [
            {
              "name": "category",
              "value": "AI Models"
            },
            {
              "name": "model_name",
              "value": "{{ $json.model_name || $json.title || '' }}"
            },
            {
              "name": "elo_score",
              "value": "{{ $json.elo_score || 'N/A' }}"
            },
            {
              "name": "context_window",
              "value": "{{ $json.context_window || 'N/A' }}"
            }
          ]
        },
        "options": {
          "keepOnlySet": true
        }
      },
      "id": "14",
      "name": "Set AI Models Data",
      "type": "n8n-nodes-base.set",
      "typeVersion": 1,
      "position": [1340, 700],
      "notes": "Formats data for AI Models with context window.\n\n**To-Do**:\n- Include training data metrics."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "getAll",
        "databaseId": "your_cloud_api_database_id",
        "options": {
          "returnAll": true
        }
      },
      "id": "15",
      "name": "Fetch Cloud APIs Existing",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [1560, 100],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Fetches existing Cloud APIs entries for AI adjustment.\n\n**To-Do**:\n- Replace 'your_cloud_api_database_id'."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "getAll",
        "databaseId": "your_local_install_database_id",
        "options": {
          "returnAll": true
        }
      },
      "id": "16",
      "name": "Fetch Local Installations Existing",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [1560, 300],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Fetches existing Local Installations entries.\n\n**To-Do**:\n- Replace 'your_local_install_database_id'."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "getAll",
        "databaseId": "your_saas_tools_database_id",
        "options": {
          "returnAll": true
        }
      },
      "id": "17",
      "name": "Fetch SaaS Tools Existing",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [1560, 500],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Fetches existing SaaS Tools entries.\n\n**To-Do**:\n- Replace 'your_saas_tools_database_id'."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "getAll",
        "databaseId": "your_ai_models_database_id",
        "options": {
          "returnAll": true
        }
      },
      "id": "18",
      "name": "Fetch AI Models Existing",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [1560, 700],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Fetches existing AI Models entries.\n\n**To-Do**:\n- Replace 'your_ai_models_database_id'."
    },
    {
      "parameters": {
        "model": "gpt-4o",
        "prompt": "You are an AI agent refining data for a Cloud APIs database. Compare the new data: {{ $json }} with old data: {{ $node['Fetch Cloud APIs Existing'].json }}. Update fields like model_name, elo_score, pricing, ensuring accuracy and consistency. Remove duplicates based on model_name. Return a JSON array of refined entries."
      },
      "id": "19",
      "name": "AI Refine Cloud APIs",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [1780, 100],
      "credentials": {
        "openAiApi": {
          "id": "openai_api_key",
          "name": "OpenAI API"
        }
      },
      "notes": "Uses ChatGPT (gpt-4o) to refine Cloud APIs data by comparing old and new entries.\n\n**To-Do**:\n- Configure OpenAI API key.\n- Test prompt for accuracy."
    },
    {
      "parameters": {
        "model": "gpt-4o",
        "prompt": "Refine data for Local Installations database. Compare new data: {{ $json }} with old data: {{ $node['Fetch Local Installations Existing'].json }}. Update fields like model_name, elo_score, hardware_requirements. Remove duplicates based on model_name. Return a JSON array."
      },
      "id": "20",
      "name": "AI Refine Local Installations",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [1780, 300],
      "credentials": {
        "openAiApi": {
          "id": "openai_api_key",
          "name": "OpenAI API"
        }
      },
      "notes": "Refines Local Installations data using ChatGPT.\n\n**To-Do**:\n- Verify prompt effectiveness."
    },
    {
      "parameters": {
        "model": "gpt-4o",
        "prompt": "Refine data for SaaS Tools database. Compare new data: {{ $json }} with old data: {{ $node['Fetch SaaS Tools Existing'].json }}. Update fields like model_name, elo_score, pricing. Remove duplicates based on model_name. Return a JSON array."
      },
      "id": "21",
      "name": "AI Refine SaaS Tools",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [1780, 500],
      "credentials": {
        "openAiApi": {
          "id": "openai_api_key",
          "name": "OpenAI API"
        }
      },
      "notes": "Refines SaaS Tools data using ChatGPT.\n\n**To-Do**:\n- Test prompt for SaaS-specific fields."
    },
    {
      "parameters": {
        "model": "gpt-4o",
        "prompt": "Refine data for AI Models database. Compare new data: {{ $json }} with old data: {{ $node['Fetch AI Models Existing'].json }}. Update fields like model_name, elo_score, context_window. Remove duplicates based on model_name. Return a JSON array."
      },
      "id": "22",
      "name": "AI Refine AI Models",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [1780, 700],
      "credentials": {
        "openAiApi": {
          "id": "openai_api_key",
          "name": "OpenAI API"
        }
      },
      "notes": "Refines AI Models data using ChatGPT.\n\n**To-Do**:\n- Ensure context_window is correctly parsed."
    },
    {
      "parameters": {
        "aggregationType": "removeDuplicates",
        "fieldToAggregateBy": "model_name"
      },
      "id": "23",
      "name": "Deduplicate Cloud APIs",
      "type": "n8n-nodes-base.aggregate",
      "typeVersion": 1,
      "position": [2000, 100],
      "notes": "Removes duplicates for Cloud APIs.\n\n**To-Do**:\n- Verify deduplication field."
    },
    {
      "parameters": {
        "aggregationType": "removeDuplicates",
        "fieldToAggregateBy": "model_name"
      },
      "id": "24",
      "name": "Deduplicate Local Installations",
      "type": "n8n-nodes-base.aggregate",
      "typeVersion": 1,
      "position": [2000, 300],
      "notes": "Removes duplicates for Local Installations.\n\n**To-Do**:\n- Ensure model_name uniqueness."
    },
    {
      "parameters": {
        "aggregationType": "removeDuplicates",
        "fieldToAggregateBy": "model_name"
      },
      "id": "25",
      "name": "Deduplicate SaaS Tools",
      "type": "n8n-nodes-base.aggregate",
      "typeVersion": 1,
      "position": [2000, 500],
      "notes": "Removes duplicates for SaaS Tools.\n\n**To-Do**:\n- Adjust for title if needed."
    },
    {
      "parameters": {
        "aggregationType": "removeDuplicates",
        "fieldToAggregateBy": "model_name"
      },
      "id": "26",
      "name": "Deduplicate AI Models",
      "type": "n8n-nodes-base.aggregate",
      "typeVersion": 1,
      "position": [2000, 700],
      "notes": "Removes duplicates for AI Models.\n\n**To-Do**:\n- Confirm deduplication logic."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "getAll",
        "databaseId": "your_cloud_api_database_id",
        "options": {
          "returnAll": true,
          "filter": {
            "filters": [
              {
                "name": "Model Name",
                "value": "{{ $json.model_name }}"
              }
            ]
          }
        }
      },
      "id": "27",
      "name": "Check Cloud APIs Existing",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [2220, 100],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Checks for existing Cloud APIs entries to avoid duplicates.\n\n**To-Do**:\n- Replace 'your_cloud_api_database_id'."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "getAll",
        "databaseId": "your_local_install_database_id",
        "options": {
          "returnAll": true,
          "filter": {
            "filters": [
              {
                "name": "Model Name",
                "value": "{{ $json.model_name }}"
              }
            ]
          }
        }
      },
      "id": "28",
      "name": "Check Local Installations Existing",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [2220, 300],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Checks for existing Local Installations entries.\n\n**To-Do**:\n- Replace 'your_local_install_database_id'."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "getAll",
        "databaseId": "your_saas_tools_database_id",
        "options": {
          "returnAll": true,
          "filter": {
            "filters": [
              {
                "name": "Model Name",
                "value": "{{ $json.model_name }}"
              }
            ]
          }
        }
      },
      "id": "29",
      "name": "Check SaaS Tools Existing",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [2220, 500],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Checks for existing SaaS Tools entries.\n\n**To-Do**:\n- Replace 'your_saas_tools_database_id'."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "getAll",
        "databaseId": "your_ai_models_database_id",
        "options": {
          "returnAll": true,
          "filter": {
            "filters": [
              {
                "name": "Model Name",
                "value": "{{ $json.model_name }}"
              }
            ]
          }
        }
      },
      "id": "30",
      "name": "Check AI Models Existing",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [2220, 700],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Checks for existing AI Models entries.\n\n**To-Do**:\n- Replace 'your_ai_models_database_id'."
    },
    {
      "parameters": {
        "conditions": {
          "number": [
            {
              "value1": "{{ $json.length }}",
              "operation": "equal",
              "value2": 0
            }
          ]
        }
      },
      "id": "31",
      "name": "Validate Cloud APIs",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [2440, 100],
      "notes": "Checks if Cloud APIs entry exists. Proceeds if none found.\n\n**To-Do**:\n- Add field validations."
    },
    {
      "parameters": {
        "conditions": {
          "number": [
            {
              "value1": "{{ $json.length }}",
              "operation": "equal",
              "value2": 0
            }
          ]
        }
      },
      "id": "32",
      "name": "Validate Local Installations",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [2440, 300],
      "notes": "Checks if Local Installations entry exists.\n\n**To-Do**:\n- Validate additional fields."
    },
    {
      "parameters": {
        "conditions": {
          "number": [
            {
              "value1": "{{ $json.length }}",
              "operation": "equal",
              "value2": 0
            }
          ]
        }
      },
      "id": "33",
      "name": "Validate SaaS Tools",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [2440, 500],
      "notes": "Checks if SaaS Tools entry exists.\n\n**To-Do**:\n- Validate pricing fields."
    },
    {
      "parameters": {
        "conditions": {
          "number": [
            {
              "value1": "{{ $json.length }}",
              "operation": "equal",
              "value2": 0
            }
          ]
        }
      },
      "id": "34",
      "name": "Validate AI Models",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [2440, 700],
      "notes": "Checks if AI Models entry exists.\n\n**To-Do**:\n- Validate context_window."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "update",
        "databaseId": "your_cloud_api_database_id",
        "properties": {
          "values": [
            {
              "name": "Model Name",
              "value": "{{ $json.model_name }}"
            },
            {
              "name": "ELO Score",
              "value": "{{ $json.elo_score }}"
            },
            {
              "name": "Category",
              "value": "{{ $json.category }}"
            },
            {
              "name": "Pricing",
              "value": "{{ $json.pricing }}"
            }
          ]
        },
        "options": {
          "upsert": true
        }
      },
      "id": "35",
      "name": "Update Cloud APIs Database",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [2660, 100],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Updates Cloud APIs database.\n\n**To-Do**:\n- Replace 'your_cloud_api_database_id'.\n- Map additional fields."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "update",
        "databaseId": "your_local_install_database_id",
        "properties": {
          "values": [
            {
              "name": "Model Name",
              "value": "{{ $json.model_name }}"
            },
            {
              "name": "ELO Score",
              "value": "{{ $json.elo_score }}"
            },
            {
              "name": "Category",
              "value": "{{ $json.category }}"
            },
            {
              "name": "Hardware Requirements",
              "value": "{{ $json.hardware_requirements }}"
            }
          ]
        },
        "options": {
          "upsert": true
        }
      },
      "id": "36",
      "name": "Update Local Installations Database",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [2660, 300],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Updates Local Installations database.\n\n**To-Do**:\n- Replace 'your_local_install_database_id'."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "update",
        "databaseId": "your_saas_tools_database_id",
        "properties": {
          "values": [
            {
              "name": "Model Name",
              "value": "{{ $json.model_name }}"
            },
            {
              "name": "ELO Score",
              "value": "{{ $json.elo_score }}"
            },
            {
              "name": "Category",
              "value": "{{ $json.category }}"
            },
            {
              "name": "Pricing",
              "value": "{{ $json.pricing }}"
            }
          ]
        },
        "options": {
          "upsert": true
        }
      },
      "id": "37",
      "name": "Update SaaS Tools Database",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [2660, 500],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Updates SaaS Tools database.\n\n**To-Do**:\n- Replace 'your_saas_tools_database_id'."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "update",
        "databaseId": "your_ai_models_database_id",
        "properties": {
          "values": [
            {
              "name": "Model Name",
              "value": "{{ $json.model_name }}"
            },
            {
              "name": "ELO Score",
              "value": "{{ $json.elo_score }}"
            },
            {
              "name": "Category",
              "value": "{{ $json.category }}"
            },
            {
              "name": "Context Window",
              "value": "{{ $json.context_window }}"
            }
          ]
        },
        "options": {
          "upsert": true
        }
      },
      "id": "38",
      "name": "Update AI Models Database",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [2660, 700],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Updates AI Models database.\n\n**To-Do**:\n- Replace 'your_ai_models_database_id'."
    },
    {
      "parameters": {
        "sendTo": "<EMAIL>",
        "subject": "AI Tools Workflow Completed",
        "message": "AI Tools Reference Page updated successfully on {{ $now }}."
      },
      "id": "39",
      "name": "Notify Success",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 1,
      "position": [2880, 400],
      "credentials": {
        "smtp": {
          "id": "smtp_credentials",
          "name": "SMTP Credentials"
        }
      },
      "notes": "Sends success notification.\n\n**To-Do**:\n- Update '<EMAIL>'.\n- Configure SMTP."
    },
    {
      "parameters": {},
      "id": "40",
      "name": "Error Trigger",
      "type": "n8n-nodes-base.errorTrigger",
      "typeVersion": 1,
      "position": [2660, 900],
      "notes": "Captures errors for notifications.\n\n**To-Do**:\n- Monitor error logs."
    },
    {
      "parameters": {
        "sendTo": "<EMAIL>",
        "subject": "AI Tools Workflow Error",
        "message": "Error in AI Tools Workflow: {{ $json.errorMessage }}"
      },
      "id": "41",
      "name": "Notify Error",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 1,
      "position": [2880, 900],
      "credentials": {
        "smtp": {
          "id": "smtp_credentials",
          "name": "SMTP Credentials"
        }
      },
      "notes": "Sends error notifications.\n\n**To-Do**:\n- Update '<EMAIL>'."
    },
    {
      "parameters": {
        "resource": "database",
        "operation": "create",
        "databaseId": "your_notion_log_database_id",
        "properties": {
          "values": [
            {
              "name": "Timestamp",
              "value": "{{ $now }}"
            },
            {
              "name": "Updated Models",
              "value": "{{ $json.length }}"
            },
            {
              "name": "Status",
              "value": "Success"
            }
          ]
        }
      },
      "id": "42",
      "name": "Log Changes",
      "type": "n8n-nodes-base.notion",
      "typeVersion": 1,
      "position": [2880, 700],
      "credentials": {
        "notionApi": {
          "id": "notion_api_key",
          "name": "Notion API"
        }
      },
      "notes": "Logs execution details.\n\n**To-Do**:\n- Replace 'your_notion_log_database_id'."
    }
  ],
  "connections": {
    "Schedule Trigger": {
      "main": [
        [
          {
            "node": "Scrape LMS Leaderboard",
            "type": "main",
            "index": 0
          },
          {
            "node": "Fetch RSS Feeds",
            "type": "main",
            "index": 0
          },
          {
            "node": "Fetch Hugging Face Models",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Scrape LMS Leaderboard": {
      "main": [
        [
          {
            "node": "Parse LMS Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Parse LMS Data": {
      "main": [
        [
          {
            "node": "Merge Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Fetch RSS Feeds": {
      "main": [
        [
          {
            "node": "Merge Data",
            "type": "main",
            "index": 1
          }
        ]
      ]
    },
    "Fetch Hugging Face Models": {
      "main": [
        [
          {
            "node": "Merge Data",
            "type": "main",
            "index": 2
          }
        ]
      ]
    },
    "Merge Data": {
      "main": [
        [
          {
            "node": "Filter Cloud APIs",
            "type": "main",
            "index": 0
          },
          {
            "node": "Filter Local Installations",
            "type": "main",
            "index": 0
          },
          {
            "node": "Filter SaaS Tools",
            "type": "main",
            "index": 0
          },
          {
            "node": "Filter AI Models",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Filter Cloud APIs": {
      "main": [
        [
          {
            "node": "Set Cloud API Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Filter Local Installations": {
      "main": [
        [
          {
            "node": "Set Local Installation Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Filter SaaS Tools": {
      "main": [
        [
          {
            "node": "Set SaaS Tools Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Filter AI Models": {
      "main": [
        [
          {
            "node": "Set AI Models Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Set Cloud API Data": {
      "main": [
        [
          {
            "node": "Fetch Cloud APIs Existing",
            "type": "main",
            "index": 0
          },
          {
            "node": "AI Refine Cloud APIs",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Set Local Installation Data": {
      "main": [
        [
          {
            "node": "Fetch Local Installations Existing",
            "type": "main",
            "index": 0
          },
          {
            "node": "AI Refine Local Installations",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Set SaaS Tools Data": {
      "main": [
        [
          {
            "node": "Fetch SaaS Tools Existing",
            "type": "main",
            "index": 0
          },
          {
            "node": "AI Refine SaaS Tools",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Set AI Models Data": {
      "main": [
        [
          {
            "node": "Fetch AI Models Existing",
            "type": "main",
            "index": 0
          },
          {
            "node": "AI Refine AI Models",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "AI Refine Cloud APIs": {
      "main": [
        [
          {
            "node": "Deduplicate Cloud APIs",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "AI Refine Local Installations": {
      "main": [
        [
          {
            "node": "Deduplicate Local Installations",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "AI Refine SaaS Tools": {
      "main": [
        [
          {
            "node": "Deduplicate SaaS Tools",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "AI Refine AI Models": {
      "main": [
        [
          {
            "node": "Deduplicate AI Models",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Deduplicate Cloud APIs": {
      "main": [
        [
          {
            "node": "Check Cloud APIs Existing",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Deduplicate Local Installations": {
      "main": [
        [
          {
            "node": "Check Local Installations Existing",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Deduplicate SaaS Tools": {
      "main": [
        [
          {
            "node": "Check SaaS Tools Existing",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Deduplicate AI Models": {
      "main": [
        [
          {
            "node": "Check AI Models Existing",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Cloud APIs Existing": {
      "main": [
        [
          {
            "node": "Validate Cloud APIs",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Local Installations Existing": {
      "main": [
        [
          {
            "node": "Validate Local Installations",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check SaaS Tools Existing": {
      "main": [
        [
          {
            "node": "Validate SaaS Tools",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check AI Models Existing": {
      "main": [
        [
          {
            "node": "Validate AI Models",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Validate Cloud APIs": {
      "main": [
        [
          {
            "node": "Update Cloud APIs Database",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Notify Error",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Validate Local Installations": {
      "main": [
        [
          {
            "node": "Update Local Installations Database",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Notify Error",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Validate SaaS Tools": {
      "main": [
        [
          {
            "node": "Update SaaS Tools Database",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Notify Error",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Validate AI Models": {
      "main": [
        [
          {
            "node": "Update AI Models Database",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Notify Error",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update Cloud APIs Database": {
      "main": [
        [
          {
            "node": "Notify Success",
            "type": "main",
            "index": 0
          },
          {
            "node": "Log Changes",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update Local Installations Database": {
      "main": [
        [
          {
            "node": "Notify Success",
            "type": "main",
            "index": 0
          },
          {
            "node": "Log Changes",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update SaaS Tools Database": {
      "main": [
        [
          {
            "node": "Notify Success",
            "type": "main",
            "index": 0
          },
          {
            "node": "Log Changes",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update AI Models Database": {
      "main": [
        [
          {
            "node": "Notify Success",
            "type": "main",
            "index": 0
          },
          {
            "node": "Log Changes",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Error Trigger": {
      "main": [
        [
          {
            "node": "Notify Error",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  ],
  "settings": {
    "timezone": "UTC",
    "active": true
  },
  "meta": {
    "notes": "Updates four Notion databases (AI Models, Cloud APIs, Local Installations, SaaS Tools) weekly with data from LMS Leaderboard, RSS feeds, and Hugging Face API, refined by ChatGPT.\n\n**To-Do List**:\n- Configure API credentials for Hugging Face, OpenAI, Notion, SMTP.\n- Replace database IDs.\n- Add RSS feed URLs.\n- Adjust filter keywords.\n- Monitor API/scraping endpoints.\n- Update email for notifications.\n- Test AI refinement prompts.\n- Add fields like api_limits, training_data.\n- Consider Google Sheets for logging."
  }
}